// Theme Management System
const THEMES = ['light', 'dark', 'pepe'];
const THEME_STORAGE_KEY = 'pepe-store-theme';

let currentTheme = 'light';

function initTheme() {
    console.log('Initializing theme system...');

    // Get theme from localStorage or default to light
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
    currentTheme = THEMES.includes(savedTheme) ? savedTheme : 'light';

    console.log('Current theme:', currentTheme);

    // Apply theme immediately
    applyTheme(currentTheme);
    updateThemeIcon(currentTheme);
}

function toggleTheme() {
    console.log('Toggling theme from:', currentTheme);

    // Cycle through themes: light -> dark -> pepe -> light
    const currentIndex = THEMES.indexOf(currentTheme);
    const nextIndex = (currentIndex + 1) % THEMES.length;
    const newTheme = THEMES[nextIndex];

    console.log('New theme:', newTheme);

    // Apply new theme
    currentTheme = newTheme;
    applyTheme(newTheme);
    updateThemeIcon(newTheme);

    // Save to localStorage
    localStorage.setItem(THEME_STORAGE_KEY, newTheme);

    // Optional: Send to server (but don't rely on it)
    fetch('/toggle-theme', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ theme: newTheme })
    }).catch(error => {
        console.warn('Could not sync theme with server:', error);
    });

    console.log('Theme changed to:', newTheme);
}

function applyTheme(theme) {
    console.log('Applying theme:', theme);

    // Remove all theme classes
    document.documentElement.removeAttribute('data-theme');

    // Apply new theme
    if (theme !== 'light') {
        document.documentElement.setAttribute('data-theme', theme);
    }

    // Force a repaint
    document.body.style.display = 'none';
    document.body.offsetHeight; // Trigger reflow
    document.body.style.display = '';
}

function updateThemeIcon(theme) {
    const themeIcon = document.getElementById('theme-icon');
    if (themeIcon) {
        // Remove all icon classes
        themeIcon.className = '';

        // Add appropriate icon
        switch(theme) {
            case 'dark':
                themeIcon.className = 'bi bi-moon-fill';
                break;
            case 'pepe':
                themeIcon.className = 'bi bi-emoji-smile-fill';
                break;
            default: // light
                themeIcon.className = 'bi bi-sun-fill';
        }

        console.log('Theme icon updated to:', themeIcon.className);
    }
}

// Auto-detect system theme preference
function detectSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark';
    }
    return 'light';
}

// Listen for system theme changes
if (window.matchMedia) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        if (!localStorage.getItem('theme')) {
            const newTheme = e.matches ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', newTheme);
            updateThemeIcon(newTheme);
        }
    });
}

// Form Validation and Enhancement
function enhanceForms() {
    // File upload validation
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            const files = e.target.files;
            const maxSize = 100 * 1024 * 1024; // 100MB

            for (let file of files) {
                if (file.size > maxSize) {
                    alert(`File "${file.name}" is too large. Maximum size is 100MB.`);
                    e.target.value = '';
                    return;
                }
            }

            // Show file names for multiple files
            if (files.length > 1) {
                const fileNames = Array.from(files).map(f => f.name).join(', ');
                const helpText = e.target.nextElementSibling;
                if (helpText && helpText.classList.contains('form-text')) {
                    helpText.textContent = `Selected: ${fileNames}`;
                }
            }
        });
    });

    // Character counters
    const textareas = document.querySelectorAll('textarea[maxlength]');
    textareas.forEach(textarea => {
        const maxLength = parseInt(textarea.getAttribute('maxlength'));
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.style.fontSize = '0.875rem';
        textarea.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - textarea.value.length;
            counter.textContent = `${remaining} characters remaining`;

            if (remaining < 50) {
                counter.classList.add('text-warning');
                counter.classList.remove('text-muted');
            } else {
                counter.classList.add('text-muted');
                counter.classList.remove('text-warning');
            }
        }

        textarea.addEventListener('input', updateCounter);
        updateCounter();
    });
}

// Loading States
function showLoading(element) {
    element.classList.add('loading');
    const originalText = element.textContent;
    element.setAttribute('data-original-text', originalText);
    element.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
    element.disabled = true;
}

function hideLoading(element) {
    element.classList.remove('loading');
    const originalText = element.getAttribute('data-original-text');
    if (originalText) {
        element.textContent = originalText;
        element.removeAttribute('data-original-text');
    }
    element.disabled = false;
}

// Search Enhancement
function enhanceSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);

            // Auto-submit search after 500ms of no typing
            searchTimeout = setTimeout(() => {
                if (e.target.value.length >= 3 || e.target.value.length === 0) {
                    e.target.form.submit();
                }
            }, 500);
        });
    }
}

// Image Loading Enhancement
function enhanceImages() {
    const images = document.querySelectorAll('img[data-src]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for browsers without IntersectionObserver
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    }
}

// Smooth Scrolling
function enableSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Toast Notifications
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container') || createToastContainer();

    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    toastContainer.appendChild(toast);

    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast element after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

// Keyboard Shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('input[name="search"]');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Ctrl/Cmd + Shift + T for theme toggle
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            toggleTheme();
        }
    });
}

// Analytics and Tracking
function trackEvent(category, action, label = null) {
    // Placeholder for analytics tracking
    console.log('Event tracked:', { category, action, label });

    // Example: Google Analytics
    // if (typeof gtag !== 'undefined') {
    //     gtag('event', action, {
    //         event_category: category,
    //         event_label: label
    //     });
    // }
}

// Error Handling
function setupErrorHandling() {
    window.addEventListener('error', function(e) {
        console.error('JavaScript error:', e.error);
        // You could send this to a logging service
    });

    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled promise rejection:', e.reason);
        // You could send this to a logging service
    });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing PEPE Store...');

    // Initialize theme system first
    initTheme();

    // Initialize other features
    enhanceForms();
    enhanceSearch();
    enhanceImages();
    enableSmoothScrolling();
    setupKeyboardShortcuts();
    setupErrorHandling();

    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }

    console.log('PEPE Store initialized successfully');
    console.log('Current theme:', currentTheme);
});

// Export functions for global use
window.toggleTheme = toggleTheme;
window.showToast = showToast;
window.trackEvent = trackEvent;
