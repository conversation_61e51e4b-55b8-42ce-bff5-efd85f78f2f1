2025-06-10 00:56:16,349 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 00:56:16,355 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 00:56:16,362 INFO:  * Restarting with stat
2025-06-10 00:56:18,531 WARNING:  * Debugger is active!
2025-06-10 00:56:18,561 INFO:  * Debugger PIN: 579-039-721
2025-06-10 00:56:21,992 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:21] "GET / HTTP/1.1" 200 -
2025-06-10 00:56:22,088 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:22] "[33mGET /static/js/app.js HTTP/1.1[0m" 404 -
2025-06-10 00:56:22,239 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:22] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 00:56:23,367 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:23] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-10 00:56:37,122 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:37] "GET / HTTP/1.1" 200 -
2025-06-10 00:56:37,166 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 00:56:37,166 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:37] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 00:56:39,881 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:39] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 00:56:41,117 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:41] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 00:56:42,859 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:42] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 00:59:49,781 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 00:59:49,784 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 00:59:49,793 INFO:  * Restarting with stat
2025-06-10 00:59:51,322 WARNING:  * Debugger is active!
2025-06-10 00:59:51,342 INFO:  * Debugger PIN: 579-039-721
2025-06-10 00:59:51,611 INFO: 127.0.0.1 - - [10/Jun/2025 00:59:51] "GET / HTTP/1.1" 200 -
2025-06-10 00:59:51,886 INFO: 127.0.0.1 - - [10/Jun/2025 00:59:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 00:59:51,890 INFO: 127.0.0.1 - - [10/Jun/2025 00:59:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 00:59:56,012 INFO: 127.0.0.1 - - [10/Jun/2025 00:59:56] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:00:00,964 INFO: 127.0.0.1 - - [10/Jun/2025 01:00:00] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:02:33,654 INFO: 127.0.0.1 - - [10/Jun/2025 01:02:33] "GET / HTTP/1.1" 200 -
2025-06-10 01:02:33,700 INFO: 127.0.0.1 - - [10/Jun/2025 01:02:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:02:33,700 INFO: 127.0.0.1 - - [10/Jun/2025 01:02:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:04:03,386 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\setup.py', reloading
2025-06-10 01:04:03,653 INFO:  * Restarting with stat
2025-06-10 01:04:05,092 WARNING:  * Debugger is active!
2025-06-10 01:04:05,107 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:04:39,371 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:04:39,372 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:04:50,359 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:50] "GET / HTTP/1.1" 200 -
2025-06-10 01:04:50,610 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:04:50,611 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:04:53,635 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:53] "GET / HTTP/1.1" 200 -
2025-06-10 01:04:53,682 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:04:53,683 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:04:55,267 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:55] "GET /admin/login HTTP/1.1" 200 -
2025-06-10 01:04:55,335 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:04:55,336 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:05:13,041 INFO: Admin action: Login - Admin logged in from 127.0.0.1
2025-06-10 01:05:13,044 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:13] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-10 01:05:13,087 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:13] "GET /admin HTTP/1.1" 200 -
2025-06-10 01:05:13,162 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:05:13,163 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:05:18,253 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:18] "GET / HTTP/1.1" 200 -
2025-06-10 01:05:18,301 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:05:18,302 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:05:52,901 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:05:52,903 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:14:51,759 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:14:51,761 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:14:52,150 INFO: 127.0.0.1 - - [10/Jun/2025 01:14:52] "GET / HTTP/1.1" 200 -
2025-06-10 01:14:52,394 INFO: 127.0.0.1 - - [10/Jun/2025 01:14:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:14:52,394 INFO: 127.0.0.1 - - [10/Jun/2025 01:14:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:15:56,727 INFO: 127.0.0.1 - - [10/Jun/2025 01:15:56] "GET / HTTP/1.1" 200 -
2025-06-10 01:15:56,769 INFO: 127.0.0.1 - - [10/Jun/2025 01:15:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:15:56,770 INFO: 127.0.0.1 - - [10/Jun/2025 01:15:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:17:35,603 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:35] "GET / HTTP/1.1" 200 -
2025-06-10 01:17:35,624 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:17:35,629 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:17:42,520 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:17:42,521 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:17:42,718 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:42] "GET / HTTP/1.1" 200 -
2025-06-10 01:17:42,823 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:17:42,824 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:17:45,271 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:45] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:17:45,845 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:45] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:22:44,082 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:22:44,083 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:22:45,365 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: app.uploaded_by

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 119, in index
    apps = query.order_by(App.created_at.desc()).paginate(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\query.py", line 98, in paginate
    return QueryPagination(
           ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 72, in __init__
    items = self._query_items()
            ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 358, in _query_items
    out = query.limit(self.per_page).offset(self._query_offset).all()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: app.uploaded_by
[SQL: SELECT app.id AS app_id, app.name AS app_name, app.description AS app_description, app.short_description AS app_short_description, app.version AS app_version, app.developer AS app_developer, app.uploaded_by AS app_uploaded_by, app.category AS app_category, app.price AS app_price, app.rating AS app_rating, app.downloads AS app_downloads, app.file_path AS app_file_path, app.file_size AS app_file_size, app.icon_path AS app_icon_path, app.created_at AS app_created_at, app.updated_at AS app_updated_at, app.is_featured AS app_is_featured 
FROM app ORDER BY app.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (12, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 01:22:45,387 INFO: 127.0.0.1 - - [10/Jun/2025 01:22:45] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:22:46,750 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: app.uploaded_by

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 119, in index
    apps = query.order_by(App.created_at.desc()).paginate(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\query.py", line 98, in paginate
    return QueryPagination(
           ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 72, in __init__
    items = self._query_items()
            ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 358, in _query_items
    out = query.limit(self.per_page).offset(self._query_offset).all()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: app.uploaded_by
[SQL: SELECT app.id AS app_id, app.name AS app_name, app.description AS app_description, app.short_description AS app_short_description, app.version AS app_version, app.developer AS app_developer, app.uploaded_by AS app_uploaded_by, app.category AS app_category, app.price AS app_price, app.rating AS app_rating, app.downloads AS app_downloads, app.file_path AS app_file_path, app.file_size AS app_file_size, app.icon_path AS app_icon_path, app.created_at AS app_created_at, app.updated_at AS app_updated_at, app.is_featured AS app_is_featured 
FROM app ORDER BY app.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (12, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 01:22:46,756 INFO: 127.0.0.1 - - [10/Jun/2025 01:22:46] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:28:58,067 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 01:28:58,068 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:28:58,073 INFO:  * Restarting with stat
2025-06-10 01:28:59,268 WARNING:  * Debugger is active!
2025-06-10 01:28:59,278 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:29:02,110 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:29:02,271 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "GET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 01:29:02,276 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "GET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 01:29:02,357 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "GET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 01:29:02,378 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 01:29:56,535 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 01:29:56,629 INFO:  * Restarting with stat
2025-06-10 01:29:57,385 WARNING:  * Debugger is active!
2025-06-10 01:29:57,391 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:30:05,609 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 01:30:05,805 INFO:  * Restarting with stat
2025-06-10 01:30:06,702 WARNING:  * Debugger is active!
2025-06-10 01:30:06,715 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:30:42,503 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 01:30:42,580 INFO:  * Restarting with stat
2025-06-10 01:30:43,273 WARNING:  * Debugger is active!
2025-06-10 01:30:43,281 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:32:00,950 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\setup.py', reloading
2025-06-10 01:32:01,026 INFO:  * Restarting with stat
2025-06-10 01:32:01,780 WARNING:  * Debugger is active!
2025-06-10 01:32:01,789 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:32:12,045 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\setup.py', reloading
2025-06-10 01:32:12,130 INFO:  * Restarting with stat
2025-06-10 01:32:12,756 WARNING:  * Debugger is active!
2025-06-10 01:32:12,763 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:32:25,076 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\setup.py', reloading
2025-06-10 01:32:25,163 INFO:  * Restarting with stat
2025-06-10 01:32:25,814 WARNING:  * Debugger is active!
2025-06-10 01:32:25,822 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:47:05,610 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 01:47:05,611 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:47:05,856 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:05] "GET / HTTP/1.1" 200 -
2025-06-10 01:47:06,284 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:47:06,304 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:06] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 01:47:27,757 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:27] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:47:28,355 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:28] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:47:28,816 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:28] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:47:29,649 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:29] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:50:26,591 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 01:50:26,593 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:50:26,603 INFO:  * Restarting with stat
2025-06-10 01:50:28,196 WARNING:  * Debugger is active!
2025-06-10 01:50:28,216 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:50:29,413 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\models.py', reloading
2025-06-10 01:50:29,657 INFO:  * Restarting with stat
2025-06-10 01:50:31,714 WARNING:  * Debugger is active!
2025-06-10 01:50:31,734 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:50:31,844 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:31] "GET /admin/login HTTP/1.1" 200 -
2025-06-10 01:50:32,136 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:50:32,137 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:50:39,193 INFO: Admin action: Login - Admin logged in from 127.0.0.1
2025-06-10 01:50:39,195 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:39] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-10 01:50:39,253 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:39] "GET /admin HTTP/1.1" 200 -
2025-06-10 01:50:39,333 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:50:39,336 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:50:40,392 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\models.py', reloading
2025-06-10 01:50:40,590 INFO:  * Restarting with stat
2025-06-10 01:50:42,370 WARNING:  * Debugger is active!
2025-06-10 01:50:42,384 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:50:48,811 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\models.py', reloading
2025-06-10 01:50:48,816 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:48] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:50:49,078 INFO:  * Restarting with stat
2025-06-10 01:50:50,609 WARNING:  * Debugger is active!
2025-06-10 01:50:50,631 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:52:47,648 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 01:52:47,649 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:52:47,655 INFO:  * Restarting with stat
2025-06-10 01:52:49,191 WARNING:  * Debugger is active!
2025-06-10 01:52:49,206 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:52:49,377 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:52:49,667 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[36mGET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 01:52:49,667 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[36mGET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 01:52:49,796 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 01:52:49,821 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 01:52:53,560 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 01:52:53,694 INFO:  * Restarting with stat
2025-06-10 02:00:40,964 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:00:40,966 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:00:43,437 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: app.external_url

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 176, in index
    apps = query.order_by(App.created_at.desc()).paginate(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\query.py", line 98, in paginate
    return QueryPagination(
           ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 72, in __init__
    items = self._query_items()
            ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 358, in _query_items
    out = query.limit(self.per_page).offset(self._query_offset).all()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: app.external_url
[SQL: SELECT app.id AS app_id, app.name AS app_name, app.description AS app_description, app.short_description AS app_short_description, app.version AS app_version, app.developer AS app_developer, app.uploaded_by AS app_uploaded_by, app.category AS app_category, app.price AS app_price, app.rating AS app_rating, app.downloads AS app_downloads, app.file_path AS app_file_path, app.external_url AS app_external_url, app.file_size AS app_file_size, app.icon_path AS app_icon_path, app.user_id AS app_user_id, app.created_at AS app_created_at, app.updated_at AS app_updated_at, app.is_featured AS app_is_featured 
FROM app ORDER BY app.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (12, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 02:00:43,467 INFO: 127.0.0.1 - - [10/Jun/2025 02:00:43] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 02:02:25,707 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:02:25,709 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:02:29,863 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:29] "GET / HTTP/1.1" 200 -
2025-06-10 02:02:30,122 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:30] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 02:02:30,124 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:30] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 02:02:34,562 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:34] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:35,515 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:35] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:36,371 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:36] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:38,029 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:38] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:42,166 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:42] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:43,857 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:43] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:44,278 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:44] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:44,710 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:44] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:45,230 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:45] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:03:02,089 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:02] "GET / HTTP/1.1" 200 -
2025-06-10 02:03:47,832 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:47] "GET / HTTP/1.1" 200 -
2025-06-10 02:03:47,875 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:03:47,877 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:03:52,497 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:52] "[32mGET /admin/login HTTP/1.1[0m" 302 -
2025-06-10 02:03:52,514 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:52] "GET /login HTTP/1.1" 200 -
2025-06-10 02:03:52,575 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:03:52,576 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:03:58,813 INFO: Admin action: Login - Admin admin logged in from 127.0.0.1
2025-06-10 02:03:58,820 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:58] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 02:03:58,872 ERROR: Exception on /admin [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 110, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 296, in admin_dashboard
    return render_template('admin/dashboard.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\base.html", line 92, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 163, in block 'content'
    <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger w-100">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin_logout'. Did you mean 'admin_login' instead?
2025-06-10 02:03:58,897 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:58] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 02:04:00,414 ERROR: Exception on /admin [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 110, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 296, in admin_dashboard
    return render_template('admin/dashboard.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\base.html", line 92, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 163, in block 'content'
    <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger w-100">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin_logout'. Did you mean 'admin_login' instead?
2025-06-10 02:04:00,424 INFO: 127.0.0.1 - - [10/Jun/2025 02:04:00] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
