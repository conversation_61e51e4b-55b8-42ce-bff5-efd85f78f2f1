2025-06-10 00:56:16,349 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 00:56:16,355 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 00:56:16,362 INFO:  * Restarting with stat
2025-06-10 00:56:18,531 WARNING:  * Debugger is active!
2025-06-10 00:56:18,561 INFO:  * Debugger PIN: 579-039-721
2025-06-10 00:56:21,992 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:21] "GET / HTTP/1.1" 200 -
2025-06-10 00:56:22,088 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:22] "[33mGET /static/js/app.js HTTP/1.1[0m" 404 -
2025-06-10 00:56:22,239 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:22] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 00:56:23,367 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:23] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-10 00:56:37,122 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:37] "GET / HTTP/1.1" 200 -
2025-06-10 00:56:37,166 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 00:56:37,166 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:37] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 00:56:39,881 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:39] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 00:56:41,117 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:41] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 00:56:42,859 INFO: 127.0.0.1 - - [10/Jun/2025 00:56:42] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 00:59:49,781 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 00:59:49,784 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 00:59:49,793 INFO:  * Restarting with stat
2025-06-10 00:59:51,322 WARNING:  * Debugger is active!
2025-06-10 00:59:51,342 INFO:  * Debugger PIN: 579-039-721
2025-06-10 00:59:51,611 INFO: 127.0.0.1 - - [10/Jun/2025 00:59:51] "GET / HTTP/1.1" 200 -
2025-06-10 00:59:51,886 INFO: 127.0.0.1 - - [10/Jun/2025 00:59:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 00:59:51,890 INFO: 127.0.0.1 - - [10/Jun/2025 00:59:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 00:59:56,012 INFO: 127.0.0.1 - - [10/Jun/2025 00:59:56] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:00:00,964 INFO: 127.0.0.1 - - [10/Jun/2025 01:00:00] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:02:33,654 INFO: 127.0.0.1 - - [10/Jun/2025 01:02:33] "GET / HTTP/1.1" 200 -
2025-06-10 01:02:33,700 INFO: 127.0.0.1 - - [10/Jun/2025 01:02:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:02:33,700 INFO: 127.0.0.1 - - [10/Jun/2025 01:02:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:04:03,386 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\setup.py', reloading
2025-06-10 01:04:03,653 INFO:  * Restarting with stat
2025-06-10 01:04:05,092 WARNING:  * Debugger is active!
2025-06-10 01:04:05,107 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:04:39,371 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:04:39,372 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:04:50,359 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:50] "GET / HTTP/1.1" 200 -
2025-06-10 01:04:50,610 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:04:50,611 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:04:53,635 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:53] "GET / HTTP/1.1" 200 -
2025-06-10 01:04:53,682 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:04:53,683 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:04:55,267 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:55] "GET /admin/login HTTP/1.1" 200 -
2025-06-10 01:04:55,335 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:04:55,336 INFO: 127.0.0.1 - - [10/Jun/2025 01:04:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:05:13,041 INFO: Admin action: Login - Admin logged in from 127.0.0.1
2025-06-10 01:05:13,044 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:13] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-10 01:05:13,087 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:13] "GET /admin HTTP/1.1" 200 -
2025-06-10 01:05:13,162 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:05:13,163 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:05:18,253 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:18] "GET / HTTP/1.1" 200 -
2025-06-10 01:05:18,301 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:05:18,302 INFO: 127.0.0.1 - - [10/Jun/2025 01:05:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:05:52,901 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:05:52,903 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:14:51,759 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:14:51,761 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:14:52,150 INFO: 127.0.0.1 - - [10/Jun/2025 01:14:52] "GET / HTTP/1.1" 200 -
2025-06-10 01:14:52,394 INFO: 127.0.0.1 - - [10/Jun/2025 01:14:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:14:52,394 INFO: 127.0.0.1 - - [10/Jun/2025 01:14:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:15:56,727 INFO: 127.0.0.1 - - [10/Jun/2025 01:15:56] "GET / HTTP/1.1" 200 -
2025-06-10 01:15:56,769 INFO: 127.0.0.1 - - [10/Jun/2025 01:15:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:15:56,770 INFO: 127.0.0.1 - - [10/Jun/2025 01:15:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:17:35,603 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:35] "GET / HTTP/1.1" 200 -
2025-06-10 01:17:35,624 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:17:35,629 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:17:42,520 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:17:42,521 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:17:42,718 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:42] "GET / HTTP/1.1" 200 -
2025-06-10 01:17:42,823 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:17:42,824 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:17:45,271 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:45] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:17:45,845 INFO: 127.0.0.1 - - [10/Jun/2025 01:17:45] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:22:44,082 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 01:22:44,083 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:22:45,365 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: app.uploaded_by

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 119, in index
    apps = query.order_by(App.created_at.desc()).paginate(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\query.py", line 98, in paginate
    return QueryPagination(
           ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 72, in __init__
    items = self._query_items()
            ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 358, in _query_items
    out = query.limit(self.per_page).offset(self._query_offset).all()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: app.uploaded_by
[SQL: SELECT app.id AS app_id, app.name AS app_name, app.description AS app_description, app.short_description AS app_short_description, app.version AS app_version, app.developer AS app_developer, app.uploaded_by AS app_uploaded_by, app.category AS app_category, app.price AS app_price, app.rating AS app_rating, app.downloads AS app_downloads, app.file_path AS app_file_path, app.file_size AS app_file_size, app.icon_path AS app_icon_path, app.created_at AS app_created_at, app.updated_at AS app_updated_at, app.is_featured AS app_is_featured 
FROM app ORDER BY app.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (12, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 01:22:45,387 INFO: 127.0.0.1 - - [10/Jun/2025 01:22:45] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:22:46,750 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: app.uploaded_by

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 119, in index
    apps = query.order_by(App.created_at.desc()).paginate(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\query.py", line 98, in paginate
    return QueryPagination(
           ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 72, in __init__
    items = self._query_items()
            ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 358, in _query_items
    out = query.limit(self.per_page).offset(self._query_offset).all()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: app.uploaded_by
[SQL: SELECT app.id AS app_id, app.name AS app_name, app.description AS app_description, app.short_description AS app_short_description, app.version AS app_version, app.developer AS app_developer, app.uploaded_by AS app_uploaded_by, app.category AS app_category, app.price AS app_price, app.rating AS app_rating, app.downloads AS app_downloads, app.file_path AS app_file_path, app.file_size AS app_file_size, app.icon_path AS app_icon_path, app.created_at AS app_created_at, app.updated_at AS app_updated_at, app.is_featured AS app_is_featured 
FROM app ORDER BY app.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (12, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 01:22:46,756 INFO: 127.0.0.1 - - [10/Jun/2025 01:22:46] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:28:58,067 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 01:28:58,068 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:28:58,073 INFO:  * Restarting with stat
2025-06-10 01:28:59,268 WARNING:  * Debugger is active!
2025-06-10 01:28:59,278 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:29:02,110 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:29:02,271 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "GET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 01:29:02,276 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "GET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 01:29:02,357 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "GET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 01:29:02,378 INFO: 127.0.0.1 - - [10/Jun/2025 01:29:02] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 01:29:56,535 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 01:29:56,629 INFO:  * Restarting with stat
2025-06-10 01:29:57,385 WARNING:  * Debugger is active!
2025-06-10 01:29:57,391 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:30:05,609 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 01:30:05,805 INFO:  * Restarting with stat
2025-06-10 01:30:06,702 WARNING:  * Debugger is active!
2025-06-10 01:30:06,715 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:30:42,503 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 01:30:42,580 INFO:  * Restarting with stat
2025-06-10 01:30:43,273 WARNING:  * Debugger is active!
2025-06-10 01:30:43,281 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:32:00,950 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\setup.py', reloading
2025-06-10 01:32:01,026 INFO:  * Restarting with stat
2025-06-10 01:32:01,780 WARNING:  * Debugger is active!
2025-06-10 01:32:01,789 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:32:12,045 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\setup.py', reloading
2025-06-10 01:32:12,130 INFO:  * Restarting with stat
2025-06-10 01:32:12,756 WARNING:  * Debugger is active!
2025-06-10 01:32:12,763 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:32:25,076 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\setup.py', reloading
2025-06-10 01:32:25,163 INFO:  * Restarting with stat
2025-06-10 01:32:25,814 WARNING:  * Debugger is active!
2025-06-10 01:32:25,822 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:47:05,610 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 01:47:05,611 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:47:05,856 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:05] "GET / HTTP/1.1" 200 -
2025-06-10 01:47:06,284 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:47:06,304 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:06] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 01:47:27,757 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:27] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:47:28,355 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:28] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:47:28,816 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:28] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:47:29,649 INFO: 127.0.0.1 - - [10/Jun/2025 01:47:29] "GET /toggle-theme HTTP/1.1" 200 -
2025-06-10 01:50:26,591 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 01:50:26,593 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:50:26,603 INFO:  * Restarting with stat
2025-06-10 01:50:28,196 WARNING:  * Debugger is active!
2025-06-10 01:50:28,216 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:50:29,413 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\models.py', reloading
2025-06-10 01:50:29,657 INFO:  * Restarting with stat
2025-06-10 01:50:31,714 WARNING:  * Debugger is active!
2025-06-10 01:50:31,734 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:50:31,844 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:31] "GET /admin/login HTTP/1.1" 200 -
2025-06-10 01:50:32,136 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:50:32,137 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:50:39,193 INFO: Admin action: Login - Admin logged in from 127.0.0.1
2025-06-10 01:50:39,195 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:39] "[32mPOST /admin/login HTTP/1.1[0m" 302 -
2025-06-10 01:50:39,253 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:39] "GET /admin HTTP/1.1" 200 -
2025-06-10 01:50:39,333 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 01:50:39,336 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 01:50:40,392 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\models.py', reloading
2025-06-10 01:50:40,590 INFO:  * Restarting with stat
2025-06-10 01:50:42,370 WARNING:  * Debugger is active!
2025-06-10 01:50:42,384 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:50:48,811 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\models.py', reloading
2025-06-10 01:50:48,816 INFO: 127.0.0.1 - - [10/Jun/2025 01:50:48] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:50:49,078 INFO:  * Restarting with stat
2025-06-10 01:50:50,609 WARNING:  * Debugger is active!
2025-06-10 01:50:50,631 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:52:47,648 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 01:52:47,649 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 01:52:47,655 INFO:  * Restarting with stat
2025-06-10 01:52:49,191 WARNING:  * Debugger is active!
2025-06-10 01:52:49,206 INFO:  * Debugger PIN: 579-039-721
2025-06-10 01:52:49,377 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 01:52:49,667 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[36mGET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 01:52:49,667 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[36mGET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 01:52:49,796 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 01:52:49,821 INFO: 127.0.0.1 - - [10/Jun/2025 01:52:49] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 01:52:53,560 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 01:52:53,694 INFO:  * Restarting with stat
2025-06-10 02:00:40,964 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:00:40,966 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:00:43,437 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlite3.OperationalError: no such column: app.external_url

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 176, in index
    apps = query.order_by(App.created_at.desc()).paginate(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\query.py", line 98, in paginate
    return QueryPagination(
           ^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 72, in __init__
    items = self._query_items()
            ^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask_sqlalchemy\pagination.py", line 358, in _query_items
    out = query.limit(self.per_page).offset(self._query_offset).all()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2704, in all
    return self._iter().all()  # type: ignore
           ^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1415, in execute
    return meth(
           ^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\sql\elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Program Files\Python311\Lib\site-packages\sqlalchemy\engine\default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such column: app.external_url
[SQL: SELECT app.id AS app_id, app.name AS app_name, app.description AS app_description, app.short_description AS app_short_description, app.version AS app_version, app.developer AS app_developer, app.uploaded_by AS app_uploaded_by, app.category AS app_category, app.price AS app_price, app.rating AS app_rating, app.downloads AS app_downloads, app.file_path AS app_file_path, app.external_url AS app_external_url, app.file_size AS app_file_size, app.icon_path AS app_icon_path, app.user_id AS app_user_id, app.created_at AS app_created_at, app.updated_at AS app_updated_at, app.is_featured AS app_is_featured 
FROM app ORDER BY app.created_at DESC
 LIMIT ? OFFSET ?]
[parameters: (12, 0)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-10 02:00:43,467 INFO: 127.0.0.1 - - [10/Jun/2025 02:00:43] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-10 02:02:25,707 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:02:25,709 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:02:29,863 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:29] "GET / HTTP/1.1" 200 -
2025-06-10 02:02:30,122 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:30] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 02:02:30,124 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:30] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 02:02:34,562 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:34] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:35,515 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:35] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:36,371 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:36] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:38,029 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:38] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:42,166 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:42] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:43,857 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:43] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:44,278 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:44] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:44,710 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:44] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:02:45,230 INFO: 127.0.0.1 - - [10/Jun/2025 02:02:45] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:03:02,089 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:02] "GET / HTTP/1.1" 200 -
2025-06-10 02:03:47,832 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:47] "GET / HTTP/1.1" 200 -
2025-06-10 02:03:47,875 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:03:47,877 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:03:52,497 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:52] "[32mGET /admin/login HTTP/1.1[0m" 302 -
2025-06-10 02:03:52,514 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:52] "GET /login HTTP/1.1" 200 -
2025-06-10 02:03:52,575 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:03:52,576 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:03:58,813 INFO: Admin action: Login - Admin admin logged in from 127.0.0.1
2025-06-10 02:03:58,820 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:58] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 02:03:58,872 ERROR: Exception on /admin [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 110, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 296, in admin_dashboard
    return render_template('admin/dashboard.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\base.html", line 92, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 163, in block 'content'
    <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger w-100">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin_logout'. Did you mean 'admin_login' instead?
2025-06-10 02:03:58,897 INFO: 127.0.0.1 - - [10/Jun/2025 02:03:58] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 02:04:00,414 ERROR: Exception on /admin [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 110, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 296, in admin_dashboard
    return render_template('admin/dashboard.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\base.html", line 92, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 163, in block 'content'
    <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger w-100">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin_logout'. Did you mean 'admin_login' instead?
2025-06-10 02:04:00,424 INFO: 127.0.0.1 - - [10/Jun/2025 02:04:00] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 02:08:06,299 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:08:06,300 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:08:06,616 ERROR: Exception on /admin [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 110, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 296, in admin_dashboard
    return render_template('admin/dashboard.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\base.html", line 92, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 163, in block 'content'
    <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger w-100">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin_logout'. Did you mean 'admin_login' instead?
2025-06-10 02:08:06,629 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:06] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 02:08:08,323 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:08] "GET / HTTP/1.1" 200 -
2025-06-10 02:08:08,580 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:08] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 02:08:08,580 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:08] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 02:08:10,352 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:10] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:08:11,279 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:11] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:08:11,679 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:11] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:08:12,127 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:12] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:08:12,559 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:12] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:08:15,398 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:15] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:08:16,047 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:16] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:08:16,704 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:16] "POST /toggle-theme HTTP/1.1" 200 -
2025-06-10 02:08:31,765 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:31] "GET / HTTP/1.1" 200 -
2025-06-10 02:08:32,026 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:32] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 02:08:32,121 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:32] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 02:08:32,772 INFO: 127.0.0.1 - - [10/Jun/2025 02:08:32] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-10 02:17:18,968 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:17:18,970 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:18:43,767 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:43] "GET / HTTP/1.1" 200 -
2025-06-10 02:18:44,067 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:44] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 02:18:44,114 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:44] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 02:18:48,911 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:48] "GET / HTTP/1.1" 200 -
2025-06-10 02:18:49,403 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:18:49,404 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:18:58,659 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:58] "GET /login HTTP/1.1" 200 -
2025-06-10 02:18:58,917 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:18:59,075 INFO: 127.0.0.1 - - [10/Jun/2025 02:18:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:19:10,313 INFO: Admin action: Login - Admin admin logged in from 127.0.0.1
2025-06-10 02:19:10,318 INFO: 127.0.0.1 - - [10/Jun/2025 02:19:10] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 02:19:10,382 ERROR: Exception on /admin [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 110, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 360, in admin_dashboard
    return render_template('admin/dashboard.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\base.html", line 96, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 163, in block 'content'
    <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger w-100">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin_logout'. Did you mean 'admin_login' instead?
2025-06-10 02:19:10,400 INFO: 127.0.0.1 - - [10/Jun/2025 02:19:10] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 02:19:36,681 INFO: 127.0.0.1 - - [10/Jun/2025 02:19:36] "GET /register HTTP/1.1" 200 -
2025-06-10 02:19:36,992 INFO: 127.0.0.1 - - [10/Jun/2025 02:19:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:19:37,309 INFO: 127.0.0.1 - - [10/Jun/2025 02:19:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:20:05,168 INFO: 127.0.0.1 - - [10/Jun/2025 02:20:05] "GET / HTTP/1.1" 200 -
2025-06-10 02:20:05,467 INFO: 127.0.0.1 - - [10/Jun/2025 02:20:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:20:05,826 INFO: 127.0.0.1 - - [10/Jun/2025 02:20:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:20:11,535 INFO: 127.0.0.1 - - [10/Jun/2025 02:20:11] "GET / HTTP/1.1" 200 -
2025-06-10 02:20:17,683 ERROR: Exception on /admin [GET]
Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 2190, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1486, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1484, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1469, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 110, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app.py", line 360, in admin_dashboard
    return render_template('admin/dashboard.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 151, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 132, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\base.html", line 96, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\templates\admin\dashboard.html", line 163, in block 'content'
    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger w-100">
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1697, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1686, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 950, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'admin_logout'. Did you mean 'admin_login' instead?
2025-06-10 02:20:17,694 INFO: 127.0.0.1 - - [10/Jun/2025 02:20:17] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 02:20:36,361 INFO: 127.0.0.1 - - [10/Jun/2025 02:20:36] "GET /register HTTP/1.1" 200 -
2025-06-10 02:20:36,604 INFO: 127.0.0.1 - - [10/Jun/2025 02:20:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:20:36,789 INFO: 127.0.0.1 - - [10/Jun/2025 02:20:36] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 02:20:55,811 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:20:55,812 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:20:55,820 INFO:  * Restarting with stat
2025-06-10 02:20:57,360 WARNING:  * Debugger is active!
2025-06-10 02:20:57,374 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:21:04,509 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:04] "[32mGET /admin/login HTTP/1.1[0m" 302 -
2025-06-10 02:21:04,590 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:04] "GET /login HTTP/1.1" 200 -
2025-06-10 02:21:04,913 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:04] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 02:21:04,913 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:04] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 02:21:13,525 INFO: Admin action: Login - Admin admin logged in from 127.0.0.1
2025-06-10 02:21:13,529 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:13] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 02:21:13,580 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:13] "GET /admin HTTP/1.1" 200 -
2025-06-10 02:21:13,626 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:21:13,630 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:21:19,925 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:19] "GET /admin/apps/add HTTP/1.1" 200 -
2025-06-10 02:21:19,971 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:21:19,972 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:21:45,471 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:45] "GET / HTTP/1.1" 200 -
2025-06-10 02:21:45,514 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:21:45,516 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:21:49,031 INFO: Admin action: Logout - Admin admin logged out
2025-06-10 02:21:49,035 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:49] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-10 02:21:49,051 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:49] "GET / HTTP/1.1" 200 -
2025-06-10 02:21:49,087 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:21:49,108 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:21:49,806 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:49] "GET /register HTTP/1.1" 200 -
2025-06-10 02:21:49,846 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:21:49,847 INFO: 127.0.0.1 - - [10/Jun/2025 02:21:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:23:06,362 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:06] "GET /login HTTP/1.1" 200 -
2025-06-10 02:23:06,405 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:23:06,408 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:23:13,349 INFO: Admin action: Login - Admin admin logged in from 127.0.0.1
2025-06-10 02:23:13,353 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:13] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 02:23:13,373 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:13] "GET /admin HTTP/1.1" 200 -
2025-06-10 02:23:13,422 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:23:13,424 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:23:22,723 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 02:23:23,033 INFO:  * Restarting with stat
2025-06-10 02:23:25,184 WARNING:  * Debugger is active!
2025-06-10 02:23:25,199 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:23:38,248 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:38] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 02:23:38,480 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:23:38,483 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:23:45,172 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:45] "GET /admin/apps/add HTTP/1.1" 200 -
2025-06-10 02:23:45,217 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:23:45,217 INFO: 127.0.0.1 - - [10/Jun/2025 02:23:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:24:40,464 INFO: Admin action: Add App - Added app: asdfsaf
2025-06-10 02:24:40,467 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:40] "[32mPOST /admin/apps/add HTTP/1.1[0m" 302 -
2025-06-10 02:24:40,505 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:40] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 02:24:40,547 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:24:40,552 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:24:40,607 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:40] "[35m[1mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:24:42,809 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "GET /app/1 HTTP/1.1" 200 -
2025-06-10 02:24:42,849 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:24:42,877 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:24:42,902 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "[35m[1mGET /uploads/uploads\\screenshots\\a629bf59-8812-407f-9f8d-3d1bf319d211_5197616963621350700.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:24:42,906 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "[35m[1mGET /uploads/uploads\\screenshots\\10b480c5-be26-4787-89f8-c4462755ca1b_5197616963621350698.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:24:42,909 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "[35m[1mGET /uploads/uploads\\screenshots\\57110e18-d9a3-493a-91cb-1a2b75016d8f_5197616963621350701.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:24:42,910 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "[35m[1mGET /uploads/uploads\\screenshots\\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:24:42,912 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "[35m[1mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:24:42,930 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:42] "[35m[1mGET /uploads/uploads\\screenshots\\13ae2485-934c-4be3-b849-6271b994bebd_5197616963621350702.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:24:52,062 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:24:55,872 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:55] "[35m[1mGET /uploads/uploads\\screenshots\\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:24:56,035 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:56] "GET /uploads/uploads\\screenshots\\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 02:24:56,036 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:56] "GET /uploads/uploads\\screenshots\\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 02:24:56,059 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:56] "GET /uploads/uploads\\screenshots\\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 02:24:56,155 INFO: 127.0.0.1 - - [10/Jun/2025 02:24:56] "[36mGET /uploads/uploads\\screenshots\\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 02:25:41,597 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 02:25:41,787 INFO:  * Restarting with stat
2025-06-10 02:25:43,113 WARNING:  * Debugger is active!
2025-06-10 02:25:43,126 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:25:47,669 INFO: 127.0.0.1 - - [10/Jun/2025 02:25:47] "GET / HTTP/1.1" 200 -
2025-06-10 02:25:47,940 INFO: 127.0.0.1 - - [10/Jun/2025 02:25:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:25:48,036 INFO: 127.0.0.1 - - [10/Jun/2025 02:25:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:25:48,054 INFO: 127.0.0.1 - - [10/Jun/2025 02:25:48] "[35m[1mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:26:01,651 INFO: Admin action: Logout - Admin admin logged out
2025-06-10 02:26:01,653 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:01] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-10 02:26:01,909 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:01] "GET / HTTP/1.1" 200 -
2025-06-10 02:26:01,981 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:26:02,269 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:02] "[35m[1mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:26:02,280 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:26:02,913 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:02] "GET /login HTTP/1.1" 200 -
2025-06-10 02:26:03,169 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:26:03,261 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:26:13,332 INFO: Admin action: Login - Admin admin logged in from 127.0.0.1
2025-06-10 02:26:13,336 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:13] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 02:26:13,682 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:13] "GET /admin HTTP/1.1" 200 -
2025-06-10 02:26:13,918 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:26:14,044 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:26:18,282 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:18] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 02:26:18,627 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:26:18,627 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:26:20,332 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:20] "GET /admin/users/add HTTP/1.1" 200 -
2025-06-10 02:26:20,685 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:26:20,686 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:26:41,711 INFO: Admin action: Create User - Admin admin created publisher account: testuser
2025-06-10 02:26:41,713 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:41] "[32mPOST /admin/users/add HTTP/1.1[0m" 302 -
2025-06-10 02:26:41,735 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:41] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 02:26:42,089 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:26:42,090 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:26:58,802 INFO: 127.0.0.1 - - [10/Jun/2025 02:26:58] "[33mPOST /admin/users/edit/2 HTTP/1.1[0m" 404 -
2025-06-10 02:27:09,635 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:27:09,636 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:27:09,642 INFO:  * Restarting with stat
2025-06-10 02:27:10,954 WARNING:  * Debugger is active!
2025-06-10 02:27:10,969 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:27:11,030 INFO: 127.0.0.1 - - [10/Jun/2025 02:27:11] "[33mPOST /admin/users/edit/2 HTTP/1.1[0m" 404 -
2025-06-10 02:27:15,387 INFO: 127.0.0.1 - - [10/Jun/2025 02:27:15] "[33mPOST /admin/users/edit/2 HTTP/1.1[0m" 404 -
2025-06-10 02:28:39,645 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 02:28:39,794 INFO:  * Restarting with stat
2025-06-10 02:28:41,190 WARNING:  * Debugger is active!
2025-06-10 02:28:41,204 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:28:56,575 INFO: Admin action: Logout - Admin admin logged out
2025-06-10 02:28:56,577 INFO: 127.0.0.1 - - [10/Jun/2025 02:28:56] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-10 02:28:56,663 INFO: 127.0.0.1 - - [10/Jun/2025 02:28:56] "GET / HTTP/1.1" 200 -
2025-06-10 02:28:56,719 INFO: 127.0.0.1 - - [10/Jun/2025 02:28:56] "[35m[1mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 500 -
2025-06-10 02:28:56,892 INFO: 127.0.0.1 - - [10/Jun/2025 02:28:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:28:56,897 INFO: 127.0.0.1 - - [10/Jun/2025 02:28:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:28:57,887 INFO: 127.0.0.1 - - [10/Jun/2025 02:28:57] "GET /login HTTP/1.1" 200 -
2025-06-10 02:28:57,930 INFO: 127.0.0.1 - - [10/Jun/2025 02:28:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:28:57,931 INFO: 127.0.0.1 - - [10/Jun/2025 02:28:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:29:04,653 WARNING: Failed login attempt for testuser from 127.0.0.1
2025-06-10 02:29:04,655 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:04] "POST /login HTTP/1.1" 200 -
2025-06-10 02:29:04,691 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:29:04,701 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:29:13,391 INFO: Admin action: Login - Publisher testuser logged in from 127.0.0.1
2025-06-10 02:29:13,398 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:13] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 02:29:13,444 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:13] "GET /publisher HTTP/1.1" 200 -
2025-06-10 02:29:13,494 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:29:13,507 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:29:17,967 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:17] "GET /publisher/apps/add HTTP/1.1" 200 -
2025-06-10 02:29:18,010 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:29:18,011 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:29:23,159 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 02:29:23,343 INFO:  * Restarting with stat
2025-06-10 02:29:25,023 WARNING:  * Debugger is active!
2025-06-10 02:29:25,038 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:29:25,205 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:25] "GET / HTTP/1.1" 200 -
2025-06-10 02:29:25,253 WARNING: File not found: uploads\uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg
2025-06-10 02:29:25,255 ERROR: Error serving file uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:25,260 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:25] "[33mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:25,436 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:29:25,443 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:29:28,801 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "GET /app/1 HTTP/1.1" 200 -
2025-06-10 02:29:28,851 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:29:28,853 WARNING: File not found: uploads\uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg
2025-06-10 02:29:28,857 WARNING: File not found: uploads\uploads\screenshots\10b480c5-be26-4787-89f8-c4462755ca1b_5197616963621350698.jpg
2025-06-10 02:29:28,857 ERROR: Error serving file uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:28,858 ERROR: Error serving file uploads\screenshots\10b480c5-be26-4787-89f8-c4462755ca1b_5197616963621350698.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:28,861 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "[33mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:28,867 WARNING: File not found: uploads\uploads\screenshots\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg
2025-06-10 02:29:28,868 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "[33mGET /uploads/uploads\\screenshots\\10b480c5-be26-4787-89f8-c4462755ca1b_5197616963621350698.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:28,869 WARNING: File not found: uploads\uploads\screenshots\a629bf59-8812-407f-9f8d-3d1bf319d211_5197616963621350700.jpg
2025-06-10 02:29:28,870 ERROR: Error serving file uploads\screenshots\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:28,873 ERROR: Error serving file uploads\screenshots\a629bf59-8812-407f-9f8d-3d1bf319d211_5197616963621350700.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:28,877 WARNING: File not found: uploads\uploads\screenshots\57110e18-d9a3-493a-91cb-1a2b75016d8f_5197616963621350701.jpg
2025-06-10 02:29:28,884 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "[33mGET /uploads/uploads\\screenshots\\a67fd676-ac37-4c9b-9f09-7c926f3bf6dd_5197616963621350699.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:28,889 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "[33mGET /uploads/uploads\\screenshots\\a629bf59-8812-407f-9f8d-3d1bf319d211_5197616963621350700.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:28,889 ERROR: Error serving file uploads\screenshots\57110e18-d9a3-493a-91cb-1a2b75016d8f_5197616963621350701.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:28,894 WARNING: File not found: uploads\uploads\screenshots\13ae2485-934c-4be3-b849-6271b994bebd_5197616963621350702.jpg
2025-06-10 02:29:28,895 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:29:28,899 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "[33mGET /uploads/uploads\\screenshots\\57110e18-d9a3-493a-91cb-1a2b75016d8f_5197616963621350701.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:28,900 ERROR: Error serving file uploads\screenshots\13ae2485-934c-4be3-b849-6271b994bebd_5197616963621350702.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:28,905 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:28] "[33mGET /uploads/uploads\\screenshots\\13ae2485-934c-4be3-b849-6271b994bebd_5197616963621350702.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:31,228 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:31] "GET / HTTP/1.1" 200 -
2025-06-10 02:29:31,264 WARNING: File not found: uploads\uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg
2025-06-10 02:29:31,265 ERROR: Error serving file uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:31,268 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:31] "[33mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:35,730 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 02:29:35,881 INFO:  * Restarting with stat
2025-06-10 02:29:37,492 WARNING:  * Debugger is active!
2025-06-10 02:29:37,508 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:29:37,675 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:37] "GET /?search=bbbbb HTTP/1.1" 200 -
2025-06-10 02:29:37,927 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:29:37,931 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:29:42,145 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:42] "GET /?search= HTTP/1.1" 200 -
2025-06-10 02:29:42,209 WARNING: File not found: uploads\uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg
2025-06-10 02:29:42,211 ERROR: Error serving file uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:29:42,213 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:29:42,215 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:29:42,216 INFO: 127.0.0.1 - - [10/Jun/2025 02:29:42] "[33mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:29:48,260 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 02:29:48,534 INFO:  * Restarting with stat
2025-06-10 02:29:50,500 WARNING:  * Debugger is active!
2025-06-10 02:29:50,519 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:32:44,679 INFO: 127.0.0.1 - - [10/Jun/2025 02:32:44] "GET /?search= HTTP/1.1" 200 -
2025-06-10 02:32:44,720 WARNING: File not found: uploads\uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg
2025-06-10 02:32:44,721 ERROR: Error serving file uploads\screenshots\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-10 02:32:44,726 INFO: 127.0.0.1 - - [10/Jun/2025 02:32:44] "[33mGET /uploads/uploads\\screenshots\\9538dbd2-db1d-43e1-99db-1e5034c60f36_5197616963621350708.jpg HTTP/1.1[0m" 404 -
2025-06-10 02:32:44,926 INFO: 127.0.0.1 - - [10/Jun/2025 02:32:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:32:44,927 INFO: 127.0.0.1 - - [10/Jun/2025 02:32:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:33:34,565 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app.py', reloading
2025-06-10 02:33:34,797 INFO:  * Restarting with stat
2025-06-10 02:33:36,407 WARNING:  * Debugger is active!
2025-06-10 02:33:36,421 INFO:  * Debugger PIN: 579-039-721
2025-06-10 02:35:37,586 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-06-10 02:35:37,587 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 02:35:39,399 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:39] "GET /?search= HTTP/1.1" 200 -
2025-06-10 02:35:39,679 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:35:39,680 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:35:42,390 INFO: Admin action: Logout - Publisher testuser logged out
2025-06-10 02:35:42,391 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:42] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-10 02:35:42,408 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:42] "GET / HTTP/1.1" 200 -
2025-06-10 02:35:42,473 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:35:42,474 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:35:43,988 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:43] "GET /login HTTP/1.1" 200 -
2025-06-10 02:35:44,045 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:35:44,045 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:35:51,357 INFO: Admin action: Login - Admin admin logged in from 127.0.0.1
2025-06-10 02:35:51,364 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:51] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 02:35:51,455 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:51] "GET /admin HTTP/1.1" 200 -
2025-06-10 02:35:51,535 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:35:51,536 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:35:54,425 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:54] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 02:35:54,481 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:35:54,484 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:35:57,733 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:57] "GET / HTTP/1.1" 200 -
2025-06-10 02:35:57,989 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:35:58,117 INFO: 127.0.0.1 - - [10/Jun/2025 02:35:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:36:02,036 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:02] "GET /admin/users/add HTTP/1.1" 200 -
2025-06-10 02:36:02,097 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:36:02,099 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:36:25,188 INFO: Admin action: Create User - Admin admin created publisher account: testuser
2025-06-10 02:36:25,190 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:25] "[32mPOST /admin/users/add HTTP/1.1[0m" 302 -
2025-06-10 02:36:25,212 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:25] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 02:36:25,271 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:36:25,275 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:36:29,863 INFO: Admin action: Edit User - Admin admin edited user: testuser
2025-06-10 02:36:29,865 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:29] "[32mPOST /admin/users/edit/2 HTTP/1.1[0m" 302 -
2025-06-10 02:36:29,884 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:29] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 02:36:29,960 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:36:29,966 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:36:32,681 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:32] "GET / HTTP/1.1" 200 -
2025-06-10 02:36:32,742 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:36:32,743 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:36:35,152 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:35] "GET /admin HTTP/1.1" 200 -
2025-06-10 02:36:35,211 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:36:35,213 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:36:41,593 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:41] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 02:36:41,647 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:36:41,652 INFO: 127.0.0.1 - - [10/Jun/2025 02:36:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:37:18,269 INFO: 127.0.0.1 - - [10/Jun/2025 02:37:18] "GET /admin HTTP/1.1" 200 -
2025-06-10 02:37:18,357 INFO: 127.0.0.1 - - [10/Jun/2025 02:37:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:37:18,634 INFO: 127.0.0.1 - - [10/Jun/2025 02:37:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:37:19,901 INFO: 127.0.0.1 - - [10/Jun/2025 02:37:19] "GET /admin/apps/add HTTP/1.1" 200 -
2025-06-10 02:37:19,980 INFO: 127.0.0.1 - - [10/Jun/2025 02:37:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:37:19,982 INFO: 127.0.0.1 - - [10/Jun/2025 02:37:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:38:01,125 INFO: Admin action: Add App - Added app: minecraft
2025-06-10 02:38:01,128 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:01] "[32mPOST /admin/apps/add HTTP/1.1[0m" 302 -
2025-06-10 02:38:01,227 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:01] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 02:38:01,301 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:38:01,304 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:01] "GET /uploads/screenshots/e4c7fd84-a909-46a1-bead-f4008ce0c94a_5469870289065206939.jpg HTTP/1.1" 200 -
2025-06-10 02:38:01,306 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:38:03,587 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:03] "GET / HTTP/1.1" 200 -
2025-06-10 02:38:03,641 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:03] "[36mGET /uploads/screenshots/e4c7fd84-a909-46a1-bead-f4008ce0c94a_5469870289065206939.jpg HTTP/1.1[0m" 304 -
2025-06-10 02:38:03,643 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:38:03,644 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:38:07,562 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /app/1 HTTP/1.1" 200 -
2025-06-10 02:38:07,620 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:38:07,622 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /uploads/screenshots/c463630f-cd47-4606-a443-f50f52f08534_5449858138057598986.jpg HTTP/1.1" 200 -
2025-06-10 02:38:07,625 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "[36mGET /uploads/screenshots/e4c7fd84-a909-46a1-bead-f4008ce0c94a_5469870289065206939.jpg HTTP/1.1[0m" 304 -
2025-06-10 02:38:07,633 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /uploads/screenshots/3072201e-a063-4b09-b9c6-20571c241718_5449858138057598985.jpg HTTP/1.1" 200 -
2025-06-10 02:38:07,649 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /uploads/screenshots/0da41e17-6a55-40a8-abea-ddd1511cd0d0_WhatsApp_Image_2025-02-19_at_9.33.10_PM.jpeg HTTP/1.1" 200 -
2025-06-10 02:38:07,657 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /uploads/screenshots/8b6d936f-f89a-4931-9c1a-a472c05fe2ee_WhatsApp_Image_2025-02-19_at_9.33.22_PM.jpeg HTTP/1.1" 200 -
2025-06-10 02:38:07,681 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /uploads/screenshots/26f92dee-6614-4981-af3f-7b06b25b5945_WhatsApp_Image_2025-03-26_at_12.52.02_PM.jpeg HTTP/1.1" 200 -
2025-06-10 02:38:07,686 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:38:07,694 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /uploads/screenshots/92adab1e-b1c2-47f0-8d5f-f466d21b5e01_5447449395843950384.jpg HTTP/1.1" 200 -
2025-06-10 02:38:07,799 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /uploads/screenshots/14fa03cd-8ce5-41bc-8342-067829db04ed_cbead6d457f86cdc2fd41f9ae72a9346.jpg HTTP/1.1" 200 -
2025-06-10 02:38:07,801 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:07] "GET /uploads/screenshots/6adfcf94-85f1-4601-8f68-d6f4fe792109_CITYPNG.COMHD_Windows_11_Logo_Icon_Transparent_Background_-_2000x2000.png HTTP/1.1" 200 -
2025-06-10 02:38:51,380 INFO: 127.0.0.1 - - [10/Jun/2025 02:38:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:39:48,874 INFO: Download logged for app ID: 1
2025-06-10 02:39:48,890 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:48] "GET /download/1 HTTP/1.1" 200 -
2025-06-10 02:39:50,514 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "GET /app/1 HTTP/1.1" 200 -
2025-06-10 02:39:50,577 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,580 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/e4c7fd84-a909-46a1-bead-f4008ce0c94a_5469870289065206939.jpg HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,581 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/c463630f-cd47-4606-a443-f50f52f08534_5449858138057598986.jpg HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,582 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/0da41e17-6a55-40a8-abea-ddd1511cd0d0_WhatsApp_Image_2025-02-19_at_9.33.10_PM.jpeg HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,584 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/3072201e-a063-4b09-b9c6-20571c241718_5449858138057598985.jpg HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,585 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/8b6d936f-f89a-4931-9c1a-a472c05fe2ee_WhatsApp_Image_2025-02-19_at_9.33.22_PM.jpeg HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,597 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,669 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/26f92dee-6614-4981-af3f-7b06b25b5945_WhatsApp_Image_2025-03-26_at_12.52.02_PM.jpeg HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,678 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/92adab1e-b1c2-47f0-8d5f-f466d21b5e01_5447449395843950384.jpg HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,690 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/14fa03cd-8ce5-41bc-8342-067829db04ed_cbead6d457f86cdc2fd41f9ae72a9346.jpg HTTP/1.1[0m" 304 -
2025-06-10 02:39:50,706 INFO: 127.0.0.1 - - [10/Jun/2025 02:39:50] "[36mGET /uploads/screenshots/6adfcf94-85f1-4601-8f68-d6f4fe792109_CITYPNG.COMHD_Windows_11_Logo_Icon_Transparent_Background_-_2000x2000.png HTTP/1.1[0m" 304 -
2025-06-10 02:40:02,170 INFO: 127.0.0.1 - - [10/Jun/2025 02:40:02] "GET / HTTP/1.1" 200 -
2025-06-10 02:40:02,228 INFO: 127.0.0.1 - - [10/Jun/2025 02:40:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 02:40:02,231 INFO: 127.0.0.1 - - [10/Jun/2025 02:40:02] "[36mGET /uploads/screenshots/e4c7fd84-a909-46a1-bead-f4008ce0c94a_5469870289065206939.jpg HTTP/1.1[0m" 304 -
2025-06-10 02:40:02,234 INFO: 127.0.0.1 - - [10/Jun/2025 02:40:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
