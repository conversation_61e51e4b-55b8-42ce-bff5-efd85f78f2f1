from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os

db = SQLAlchemy()

class App(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    short_description = db.Column(db.String(200), nullable=False)
    version = db.Column(db.String(20), nullable=False)
    developer = db.Column(db.String(100), nullable=False)
    uploaded_by = db.Column(db.String(100), nullable=False, default='Admin')
    category = db.Column(db.String(50), nullable=False)
    price = db.Column(db.Float, default=0.0)
    rating = db.Column(db.Float, default=0.0)
    downloads = db.Column(db.Integer, default=0)
    file_path = db.Column(db.String(200), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)  # in bytes
    icon_path = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_featured = db.Column(db.Boolean, default=False)

    # Relationships
    screenshots = db.relationship('Screenshot', backref='app', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<App {self.name}>'

    def get_file_size_formatted(self):
        """Return formatted file size"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    def increment_downloads(self):
        """Increment download count"""
        self.downloads += 1
        db.session.commit()

class Screenshot(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.Integer, db.ForeignKey('app.id'), nullable=False)
    file_path = db.Column(db.String(200), nullable=False)
    caption = db.Column(db.String(200))
    order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Screenshot {self.file_path}>'

class DownloadLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.Integer, db.ForeignKey('app.id'), nullable=False)
    ip_address = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.String(500))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship
    app = db.relationship('App', backref='download_logs')

    def __repr__(self):
        return f'<DownloadLog {self.app_id} at {self.timestamp}>'

class AdminLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    action = db.Column(db.String(100), nullable=False)
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<AdminLog {self.action} at {self.timestamp}>'
