from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
import os

db = SQLAlchemy()

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='publisher')  # 'admin' or 'publisher'
    is_active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def is_admin(self):
        return self.role == 'admin'

    def is_publisher(self):
        return self.role in ['admin', 'publisher']

    def update_last_login(self):
        """Update the last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()

    def __repr__(self):
        return f'<User {self.username}>'

class App(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    short_description = db.Column(db.String(200), nullable=False)
    version = db.Column(db.String(20), nullable=False)
    developer = db.Column(db.String(100), nullable=False)
    uploaded_by = db.Column(db.String(100), nullable=False, default='Admin')
    category = db.Column(db.String(50), nullable=False)
    price = db.Column(db.Float, default=0.0)
    rating = db.Column(db.Float, default=0.0)
    downloads = db.Column(db.Integer, default=0)
    file_path = db.Column(db.String(200))  # Local file path (optional)
    external_url = db.Column(db.String(500))  # External download URL (optional)
    file_size = db.Column(db.Integer, default=0)  # in bytes
    icon_path = db.Column(db.String(200))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # Who uploaded it
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_featured = db.Column(db.Boolean, default=False)

    # Relationships
    screenshots = db.relationship('Screenshot', backref='app', lazy=True, cascade='all, delete-orphan')
    user = db.relationship('User', backref='apps')

    def __repr__(self):
        return f'<App {self.name}>'

    def get_file_size_formatted(self):
        """Return formatted file size"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    def increment_downloads(self):
        """Increment download count"""
        self.downloads += 1
        db.session.commit()

    def has_file(self):
        """Check if app has a downloadable file"""
        return bool(self.file_path or self.external_url)

    def get_download_url(self):
        """Get the download URL (external or local)"""
        if self.external_url:
            return self.external_url
        elif self.file_path:
            return f"/download/{self.id}"
        return None

    def is_external_download(self):
        """Check if download is external"""
        return bool(self.external_url)

    def get_average_rating(self):
        """Calculate average rating"""
        if not self.ratings:
            return 0
        return sum(rating.rating for rating in self.ratings) / len(self.ratings)

    def get_rating_count(self):
        """Get total number of ratings"""
        return len(self.ratings)

    def get_rating_distribution(self):
        """Get rating distribution (1-5 stars)"""
        distribution = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
        for rating in self.ratings:
            distribution[rating.rating] += 1
        return distribution

    def get_star_display(self):
        """Get star display string"""
        avg_rating = self.get_average_rating()
        full_stars = int(avg_rating)
        half_star = avg_rating - full_stars >= 0.5
        empty_stars = 5 - full_stars - (1 if half_star else 0)

        stars = '★' * full_stars
        if half_star:
            stars += '☆'
        stars += '☆' * empty_stars

        return stars

class Screenshot(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.Integer, db.ForeignKey('app.id'), nullable=False)
    file_path = db.Column(db.String(200), nullable=False)
    caption = db.Column(db.String(200))
    order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Screenshot {self.file_path}>'

class DownloadLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.Integer, db.ForeignKey('app.id'), nullable=False)
    ip_address = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.String(500))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship
    app = db.relationship('App', backref='download_logs')

    def __repr__(self):
        return f'<DownloadLog {self.app_id} at {self.timestamp}>'

class AdminLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    username = db.Column(db.String(80), nullable=False)
    action = db.Column(db.String(100), nullable=False)
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.String(500))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship
    user = db.relationship('User', backref='admin_logs')

    def __repr__(self):
        return f'<AdminLog {self.username}: {self.action} at {self.timestamp}>'

class LoginLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    username = db.Column(db.String(80), nullable=False)
    success = db.Column(db.Boolean, nullable=False)
    ip_address = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.String(500))
    failure_reason = db.Column(db.String(200))  # For failed logins
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship
    user = db.relationship('User', backref='login_logs')

    def __repr__(self):
        status = "SUCCESS" if self.success else "FAILED"
        return f'<LoginLog {self.username}: {status} at {self.timestamp}>'

class ActivityLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    username = db.Column(db.String(80), nullable=False)
    action = db.Column(db.String(100), nullable=False)  # 'view_app', 'download_app', 'search', etc.
    target_type = db.Column(db.String(50))  # 'app', 'user', 'system'
    target_id = db.Column(db.Integer)  # ID of the target (app_id, user_id, etc.)
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45), nullable=False)
    user_agent = db.Column(db.String(500))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship
    user = db.relationship('User', backref='activity_logs')

    def __repr__(self):
        return f'<ActivityLog {self.username}: {self.action} at {self.timestamp}>'

class AppRating(db.Model):
    """App rating system"""
    id = db.Column(db.Integer, primary_key=True)
    app_id = db.Column(db.Integer, db.ForeignKey('app.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)  # Nullable for anonymous ratings
    rating = db.Column(db.Integer, nullable=False)  # 1-5 stars
    review = db.Column(db.Text)  # Optional review text
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    ip_address = db.Column(db.String(45))  # For anonymous rating tracking

    # Relationships
    app = db.relationship('App', backref='ratings')
    user = db.relationship('User', backref='ratings')

    def __repr__(self):
        return f'<AppRating {self.app_id}: {self.rating} stars>'
