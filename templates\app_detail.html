{% extends "base.html" %}

{% block title %}{{ app.name }} - App Store{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('index', category=app.category) }}">{{ app.category }}</a></li>
            <li class="breadcrumb-item active">{{ app.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- App Info -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            {% if app.icon_path %}
                            <img src="{{ url_for('uploaded_file', filename=app.icon_path) }}"
                                 class="img-fluid rounded app-detail-icon" alt="{{ app.name }}">
                            {% else %}
                            <div class="app-detail-icon-placeholder">
                                <i class="bi bi-app"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-9">
                            <h1 class="mb-2">{{ app.name }}</h1>
                            <p class="text-muted mb-2">by {{ app.developer }}</p>
                            <p class="lead">{{ app.short_description }}</p>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Category:</strong>
                                    <span class="badge bg-secondary">{{ app.category }}</span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Rating:</strong>
                                    <span class="text-warning">★★★★☆</span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Size:</strong> {{ app.get_file_size_formatted() }}
                                </div>
                                <div class="col-sm-6">
                                    <strong>Downloads:</strong> {{ app.downloads }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Uploaded by:</strong> {{ app.uploaded_by }}
                                </div>
                                <div class="col-sm-6">
                                    <strong>Version:</strong> {{ app.version }}
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-sm-6">
                                    <h3 class="text-primary">
                                        {% if app.price == 0 %}
                                            FREE
                                        {% else %}
                                            ${{ "%.2f"|format(app.price) }}
                                        {% endif %}
                                    </h3>
                                </div>
                                <div class="col-sm-6">
                                    {% if app.has_file() %}
                                        {% if app.price == 0 %}
                                            <a href="{{ app.get_download_url() }}"
                                               class="btn btn-success btn-lg"
                                               {% if app.is_external_download() %}target="_blank"{% endif %}>
                                                <i class="bi bi-download"></i>
                                                {% if app.is_external_download() %}
                                                    Download (External)
                                                {% else %}
                                                    Download
                                                {% endif %}
                                            </a>
                                        {% else %}
                                            <a href="{{ app.get_download_url() }}"
                                               class="btn btn-warning btn-lg">
                                                <i class="bi bi-credit-card"></i> Buy Now
                                            </a>
                                        {% endif %}
                                    {% else %}
                                        <button class="btn btn-secondary btn-lg" disabled>
                                            <i class="bi bi-x-circle"></i> Not Available
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rating Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Ratings & Reviews</h3>
                </div>
                <div class="card-body">
                    <!-- Rating Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="text-center">
                                <h2 class="display-4 text-primary">
                                    {% set avg_rating = app.get_average_rating() %}
                                    {% if avg_rating > 0 %}
                                        {{ "%.1f"|format(avg_rating) }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </h2>
                                <div class="rating-stars mb-2">
                                    {% for i in range(1, 6) %}
                                        {% if i <= avg_rating %}
                                            <i class="bi bi-star-fill text-warning fs-4"></i>
                                        {% elif i - 0.5 <= avg_rating %}
                                            <i class="bi bi-star-half text-warning fs-4"></i>
                                        {% else %}
                                            <i class="bi bi-star text-muted fs-4"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <small class="text-muted">
                                    {% if app.get_rating_count() > 0 %}
                                        {{ app.get_rating_count() }} rating{{ 's' if app.get_rating_count() != 1 else '' }}
                                    {% else %}
                                        No ratings yet
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Rating Distribution -->
                            {% set distribution = app.get_rating_distribution() %}
                            {% set total_ratings = app.get_rating_count() %}
                            {% for star in range(5, 0, -1) %}
                            <div class="d-flex align-items-center mb-1">
                                <span class="me-2">{{ star }} <i class="bi bi-star-fill text-warning"></i></span>
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    {% set percentage = (distribution[star] / total_ratings * 100) if total_ratings > 0 else 0 %}
                                    <div class="progress-bar bg-warning" style="width: {{ percentage }}%"></div>
                                </div>
                                <small class="text-muted">{{ distribution[star] }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Rate This App -->
                    <div class="border-top pt-4">
                        <h5>Rate This App</h5>
                        <form method="POST" action="{{ url_for('rate_app', app_id=app.id) }}">
                            <div class="mb-3">
                                <label class="form-label">Your Rating:</label>
                                <div class="rating-input">
                                    {% for i in range(1, 6) %}
                                    <input type="radio" name="rating" value="{{ i }}" id="star{{ i }}" required>
                                    <label for="star{{ i }}" class="star-label">
                                        <i class="bi bi-star"></i>
                                    </label>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="review" class="form-label">Review (Optional):</label>
                                <textarea class="form-control" id="review" name="review" rows="3"
                                          placeholder="Share your thoughts about this app..."></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-star"></i> Submit Rating
                            </button>
                        </form>
                    </div>

                    <!-- Existing Reviews -->
                    {% if app.ratings %}
                    <div class="border-top pt-4 mt-4">
                        <h5>User Reviews</h5>
                        {% for rating in app.ratings[:5] %}
                        <div class="border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                            {% if i <= rating.rating %}
                                                <i class="bi bi-star-fill text-warning"></i>
                                            {% else %}
                                                <i class="bi bi-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted">
                                        by {{ rating.user.username if rating.user else 'Anonymous' }}
                                        on {{ rating.timestamp.strftime('%B %d, %Y') }}
                                    </small>
                                </div>
                            </div>
                            {% if rating.review %}
                            <p class="mt-2 mb-0">{{ rating.review }}</p>
                            {% endif %}
                        </div>
                        {% endfor %}
                        {% if app.ratings|length > 5 %}
                        <small class="text-muted">... and {{ app.ratings|length - 5 }} more reviews</small>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Description -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Description</h3>
                </div>
                <div class="card-body">
                    <p>{{ app.description | nl2br | safe }}</p>
                </div>
            </div>

            <!-- Screenshots -->
            {% if app.screenshots %}
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Screenshots</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for screenshot in app.screenshots %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <img src="{{ url_for('uploaded_file', filename=screenshot.file_path) }}"
                                 class="img-fluid rounded screenshot-thumb"
                                 alt="Screenshot {{ loop.index }}"
                                 data-bs-toggle="modal"
                                 data-bs-target="#screenshotModal{{ loop.index }}">
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Screenshot Modals -->
            {% for screenshot in app.screenshots %}
            <div class="modal fade" id="screenshotModal{{ loop.index }}" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Screenshot {{ loop.index }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="{{ url_for('uploaded_file', filename=screenshot.file_path) }}"
                                 class="img-fluid" alt="Screenshot {{ loop.index }}">
                            {% if screenshot.caption %}
                            <p class="mt-2">{{ screenshot.caption }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- App Stats -->
            <div class="card">
                <div class="card-header">
                    <h5>App Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Released:</strong><br>
                        <small class="text-muted">{{ app.created_at.strftime('%B %d, %Y') }}</small>
                    </div>
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">{{ app.updated_at.strftime('%B %d, %Y') }}</small>
                    </div>
                    {% if app.is_featured %}
                    <div class="mb-3">
                        <span class="badge bg-warning text-dark">
                            <i class="bi bi-star-fill"></i> Featured
                        </span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Related Apps -->
            {% if related_apps %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Related Apps</h5>
                </div>
                <div class="card-body">
                    {% for related_app in related_apps %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            {% if related_app.icon_path %}
                            <img src="{{ url_for('uploaded_file', filename=related_app.icon_path) }}"
                                 class="related-app-icon" alt="{{ related_app.name }}">
                            {% else %}
                            <div class="related-app-icon-placeholder">
                                <i class="bi bi-app"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="{{ url_for('app_detail', app_id=related_app.id) }}" class="text-decoration-none">
                                    {{ related_app.name }}
                                </a>
                            </h6>
                            <p class="mb-1 small text-muted">{{ related_app.developer }}</p>
                            <small class="text-muted">
                                {% if related_app.price == 0 %}Free{% else %}&dollar;{{ "%.2f"|format(related_app.price) }}{% endif %}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.app-detail-icon {
    width: 128px;
    height: 128px;
    object-fit: cover;
}

.app-detail-icon-placeholder {
    width: 128px;
    height: 128px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #6c757d;
    border-radius: 0.375rem;
}

.screenshot-thumb {
    cursor: pointer;
    transition: transform 0.2s;
}

.screenshot-thumb:hover {
    transform: scale(1.05);
}

.related-app-icon {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 0.375rem;
}

.related-app-icon-placeholder {
    width: 48px;
    height: 48px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
    border-radius: 0.375rem;
}

/* Rating System Styles */
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input[type="radio"] {
    display: none;
}

.rating-input .star-label {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s;
    margin-right: 0.25rem;
}

.rating-input .star-label:hover,
.rating-input .star-label:hover ~ .star-label,
.rating-input input[type="radio"]:checked ~ .star-label {
    color: #ffc107;
}

.rating-input .star-label:hover {
    transform: scale(1.1);
}

.rating-stars {
    display: inline-block;
}

.rating-stars i {
    margin-right: 0.1rem;
}
</style>
{% endblock %}
