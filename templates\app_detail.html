{% extends "base.html" %}

{% block title %}{{ app.name }} - App Store{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('index', category=app.category) }}">{{ app.category }}</a></li>
            <li class="breadcrumb-item active">{{ app.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- App Info -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            {% if app.icon_path %}
                            <img src="{{ url_for('uploaded_file', filename=app.icon_path.replace('uploads/', '')) }}"
                                 class="img-fluid rounded app-detail-icon" alt="{{ app.name }}">
                            {% else %}
                            <div class="app-detail-icon-placeholder">
                                <i class="bi bi-app"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-9">
                            <h1 class="mb-2">{{ app.name }}</h1>
                            <p class="text-muted mb-2">by {{ app.developer }}</p>
                            <p class="lead">{{ app.short_description }}</p>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Category:</strong>
                                    <span class="badge bg-secondary">{{ app.category }}</span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Rating:</strong>
                                    <span class="text-warning">★★★★☆</span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Size:</strong> {{ app.get_file_size_formatted() }}
                                </div>
                                <div class="col-sm-6">
                                    <strong>Downloads:</strong> {{ app.downloads }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Uploaded by:</strong> {{ app.uploaded_by }}
                                </div>
                                <div class="col-sm-6">
                                    <strong>Version:</strong> {{ app.version }}
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-sm-6">
                                    <h3 class="text-primary">
                                        {% if app.price == 0 %}
                                            FREE
                                        {% else %}
                                            ${{ "%.2f"|format(app.price) }}
                                        {% endif %}
                                    </h3>
                                </div>
                                <div class="col-sm-6">
                                    {% if app.has_file() %}
                                        {% if app.price == 0 %}
                                            <a href="{{ app.get_download_url() }}"
                                               class="btn btn-success btn-lg"
                                               {% if app.is_external_download() %}target="_blank"{% endif %}>
                                                <i class="bi bi-download"></i>
                                                {% if app.is_external_download() %}
                                                    Download (External)
                                                {% else %}
                                                    Download
                                                {% endif %}
                                            </a>
                                        {% else %}
                                            <a href="{{ app.get_download_url() }}"
                                               class="btn btn-warning btn-lg">
                                                <i class="bi bi-credit-card"></i> Buy Now
                                            </a>
                                        {% endif %}
                                    {% else %}
                                        <button class="btn btn-secondary btn-lg" disabled>
                                            <i class="bi bi-x-circle"></i> Not Available
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Description</h3>
                </div>
                <div class="card-body">
                    <p>{{ app.description | nl2br | safe }}</p>
                </div>
            </div>

            <!-- Screenshots -->
            {% if app.screenshots %}
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Screenshots</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for screenshot in app.screenshots %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <img src="{{ url_for('uploaded_file', filename=screenshot.file_path.replace('uploads/', '')) }}"
                                 class="img-fluid rounded screenshot-thumb"
                                 alt="Screenshot {{ loop.index }}"
                                 data-bs-toggle="modal"
                                 data-bs-target="#screenshotModal{{ loop.index }}">
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Screenshot Modals -->
            {% for screenshot in app.screenshots %}
            <div class="modal fade" id="screenshotModal{{ loop.index }}" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Screenshot {{ loop.index }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="{{ url_for('uploaded_file', filename=screenshot.file_path.replace('uploads/', '')) }}"
                                 class="img-fluid" alt="Screenshot {{ loop.index }}">
                            {% if screenshot.caption %}
                            <p class="mt-2">{{ screenshot.caption }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- App Stats -->
            <div class="card">
                <div class="card-header">
                    <h5>App Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Released:</strong><br>
                        <small class="text-muted">{{ app.created_at.strftime('%B %d, %Y') }}</small>
                    </div>
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">{{ app.updated_at.strftime('%B %d, %Y') }}</small>
                    </div>
                    {% if app.is_featured %}
                    <div class="mb-3">
                        <span class="badge bg-warning text-dark">
                            <i class="bi bi-star-fill"></i> Featured
                        </span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Related Apps -->
            {% if related_apps %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Related Apps</h5>
                </div>
                <div class="card-body">
                    {% for related_app in related_apps %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            {% if related_app.icon_path %}
                            <img src="{{ url_for('uploaded_file', filename=related_app.icon_path.replace('uploads/', '')) }}"
                                 class="related-app-icon" alt="{{ related_app.name }}">
                            {% else %}
                            <div class="related-app-icon-placeholder">
                                <i class="bi bi-app"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="{{ url_for('app_detail', app_id=related_app.id) }}" class="text-decoration-none">
                                    {{ related_app.name }}
                                </a>
                            </h6>
                            <p class="mb-1 small text-muted">{{ related_app.developer }}</p>
                            <small class="text-muted">
                                {% if related_app.price == 0 %}Free{% else %}&dollar;{{ "%.2f"|format(related_app.price) }}{% endif %}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.app-detail-icon {
    width: 128px;
    height: 128px;
    object-fit: cover;
}

.app-detail-icon-placeholder {
    width: 128px;
    height: 128px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #6c757d;
    border-radius: 0.375rem;
}

.screenshot-thumb {
    cursor: pointer;
    transition: transform 0.2s;
}

.screenshot-thumb:hover {
    transform: scale(1.05);
}

.related-app-icon {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 0.375rem;
}

.related-app-icon-placeholder {
    width: 48px;
    height: 48px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
    border-radius: 0.375rem;
}
</style>
{% endblock %}
