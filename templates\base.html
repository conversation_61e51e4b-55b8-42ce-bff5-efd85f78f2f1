<!DOCTYPE html>
<html lang="en" data-theme="{{ current_theme }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PEPE Store - Top #1 Tools for PC{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand pepe-brand" href="{{ url_for('index') }}">
                <span class="pepe-logo">🐸</span> PEPE Store
                <span class="pepe-tagline">Top #1 Tools for PC</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">Home</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <!-- Theme Toggle -->
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm me-2" onclick="toggleTheme()" title="Switch Theme">
                            <i class="bi bi-sun-fill" id="theme-icon"></i>
                        </button>
                    </li>

                    <!-- User Menu - Only show if logged in -->
                    {% if session.user_id %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> {{ session.username }}
                        </a>
                        <ul class="dropdown-menu">
                            {% if session.role == 'admin' %}
                            <li><a class="dropdown-item" href="{{ url_for('admin_dashboard') }}">
                                <i class="bi bi-speedometer2"></i> Admin Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin_apps') }}">
                                <i class="bi bi-list"></i> Manage Apps
                            </a></li>
                            {% else %}
                            <li><a class="dropdown-item" href="{{ url_for('publisher_dashboard') }}">
                                <i class="bi bi-house"></i> My Dashboard
                            </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>🐸 PEPE Store</h5>
                    <p class="mb-0">Top #1 destination for PC tools and applications</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">&copy; 2025 PEPE Store. Powered by the community.</p>
                    {% if not session.user_id %}
                    <small class="text-muted">
                        <a href="{{ url_for('login') }}" class="text-decoration-none">Publisher Login</a>
                    </small>
                    {% endif %}
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>

    {% block extra_scripts %}{% endblock %}
</body>
</html>
