#!/usr/bin/env python3
"""
Database Migration Script for PEPE Store
Adds the uploaded_by column to existing apps
"""

import os
import sqlite3
from app import app, db
from models import App

def migrate_database():
    """Migrate database to add uploaded_by column"""
    print("🔄 Migrating database...")
    
    db_path = 'app_store.db'
    
    if not os.path.exists(db_path):
        print("ℹ️  No existing database found, creating new one...")
        with app.app_context():
            db.create_all()
            print("✅ New database created successfully")
        return True
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if uploaded_by column exists
        cursor.execute("PRAGMA table_info(app)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'uploaded_by' not in columns:
            print("➕ Adding uploaded_by column...")
            cursor.execute("ALTER TABLE app ADD COLUMN uploaded_by VARCHAR(100) DEFAULT 'Admin'")
            
            # Update existing records
            cursor.execute("UPDATE app SET uploaded_by = 'Admin' WHERE uploaded_by IS NULL")
            
            conn.commit()
            print("✅ uploaded_by column added successfully")
        else:
            print("ℹ️  uploaded_by column already exists")
        
        conn.close()
        
        # Verify with SQLAlchemy
        with app.app_context():
            count = App.query.count()
            print(f"📊 Database verified: {count} apps found")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def main():
    """Main migration function"""
    print("🐸 PEPE Store - Database Migration")
    print("=" * 50)
    
    if migrate_database():
        print("\n✅ Migration completed successfully!")
        print("You can now run the application with: py -3.11 run.py")
    else:
        print("\n❌ Migration failed!")
        print("You may need to delete the database and run setup again.")
    
    print("=" * 50)

if __name__ == '__main__':
    main()
