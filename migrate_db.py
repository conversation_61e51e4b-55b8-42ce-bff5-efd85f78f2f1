#!/usr/bin/env python3
"""
Database Migration Script for PEPE Store
Migrates to new user system and removes dummy data
"""

import os
import sqlite3
from app import app, db
from models import App, User

def migrate_database():
    """Migrate database to new schema"""
    print("🔄 Migrating database...")

    db_path = 'app_store.db'

    if not os.path.exists(db_path):
        print("ℹ️  No existing database found, creating new one...")
        with app.app_context():
            db.create_all()
            create_default_admin()
            print("✅ New database created successfully")
        return True

    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check if user table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user'")
        user_table_exists = cursor.fetchone() is not None

        if not user_table_exists:
            print("➕ Creating user table...")
            with app.app_context():
                db.create_all()
                create_default_admin()

        # Check and add new columns to app table
        cursor.execute("PRAGMA table_info(app)")
        columns = [column[1] for column in cursor.fetchall()]

        if 'external_url' not in columns:
            print("➕ Adding external_url column...")
            cursor.execute("ALTER TABLE app ADD COLUMN external_url VARCHAR(500)")

        if 'user_id' not in columns:
            print("➕ Adding user_id column...")
            cursor.execute("ALTER TABLE app ADD COLUMN user_id INTEGER")

            # Set default user_id to 1 (admin) for existing apps
            cursor.execute("UPDATE app SET user_id = 1 WHERE user_id IS NULL")

        conn.commit()
        conn.close()

        # Remove dummy data
        remove_dummy_data()

        # Verify with SQLAlchemy
        with app.app_context():
            app_count = App.query.count()
            user_count = User.query.count()
            print(f"📊 Database verified: {user_count} users, {app_count} apps")

        return True

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def create_default_admin():
    """Create default admin user"""
    print("👤 Creating default admin user...")

    # Check if admin already exists
    existing_admin = User.query.filter_by(username='admin').first()
    if existing_admin:
        print("ℹ️  Admin user already exists")
        return

    admin = User(
        username='admin',
        email='<EMAIL>',
        role='admin'
    )
    admin.set_password('admin123')

    db.session.add(admin)
    db.session.commit()
    print("✅ Default admin user created (admin/admin123)")

def remove_dummy_data():
    """Remove any dummy/sample data"""
    print("🗑️  Removing dummy data...")

    with app.app_context():
        # Remove apps with sample names
        dummy_names = ['PEPE Code Editor', 'PEPE Image Viewer', 'PEPE Task Master',
                      'Text Editor Pro', 'Photo Viewer Plus', 'Task Manager']

        for name in dummy_names:
            dummy_app = App.query.filter_by(name=name).first()
            if dummy_app:
                print(f"   Removing: {name}")
                # Delete associated files
                if dummy_app.file_path and os.path.exists(dummy_app.file_path):
                    os.remove(dummy_app.file_path)
                if dummy_app.icon_path and os.path.exists(dummy_app.icon_path):
                    os.remove(dummy_app.icon_path)

                db.session.delete(dummy_app)

        db.session.commit()
        print("✅ Dummy data removed")

def main():
    """Main migration function"""
    print("🐸 PEPE Store - Database Migration")
    print("=" * 50)

    if migrate_database():
        print("\n✅ Migration completed successfully!")
        print("You can now run the application with: py -3.11 run.py")
    else:
        print("\n❌ Migration failed!")
        print("You may need to delete the database and run setup again.")

    print("=" * 50)

if __name__ == '__main__':
    main()
