{% extends "base.html" %}

{% block title %}Register - PEPE Store{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card">
                <div class="card-header text-center">
                    <h3><i class="bi bi-person-plus"></i> Create Account</h3>
                    <p class="text-muted mb-0">Join the PEPE Store community</p>
                </div>
                <div class="card-body">
                    <form method="POST" id="registerForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   minlength="3" maxlength="50" required>
                            <div class="form-text">At least 3 characters, letters and numbers only</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">We'll never share your email with anyone</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="role" class="form-label">Account Type *</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Select your role</option>
                                <option value="publisher">Publisher - Upload and manage apps</option>
                                <option value="admin">Administrator - Full system access</option>
                            </select>
                            <div class="form-text">
                                <strong>Publisher:</strong> Can upload and manage their own apps<br>
                                <strong>Admin:</strong> Can manage all apps and users
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Password *</label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   minlength="6" required>
                            <div class="form-text">At least 6 characters</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password *</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                   minlength="6" required>
                            <div class="invalid-feedback" id="password-mismatch" style="display: none;">
                                Passwords do not match
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" class="text-decoration-none">Terms of Service</a> 
                                    and <a href="#" class="text-decoration-none">Privacy Policy</a>
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-person-plus"></i> Create Account
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        Already have an account? 
                        <a href="{{ url_for('login') }}" class="text-decoration-none">Login here</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Form validation
document.getElementById('registerForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const passwordMismatch = document.getElementById('password-mismatch');
    
    if (password !== confirmPassword) {
        e.preventDefault();
        passwordMismatch.style.display = 'block';
        document.getElementById('confirm_password').classList.add('is-invalid');
        return false;
    } else {
        passwordMismatch.style.display = 'none';
        document.getElementById('confirm_password').classList.remove('is-invalid');
    }
});

// Real-time password matching
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    const passwordMismatch = document.getElementById('password-mismatch');
    
    if (confirmPassword && password !== confirmPassword) {
        passwordMismatch.style.display = 'block';
        this.classList.add('is-invalid');
    } else {
        passwordMismatch.style.display = 'none';
        this.classList.remove('is-invalid');
    }
});

// Username validation
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const regex = /^[a-zA-Z0-9_]+$/;
    
    if (username && !regex.test(username)) {
        this.setCustomValidity('Username can only contain letters, numbers, and underscores');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
